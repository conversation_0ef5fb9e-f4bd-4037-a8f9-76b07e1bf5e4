-- Script to create a default Captain assistant for account ID 1
-- This is needed because the copilot functionality requires an assistant to work

-- Check if there are any assistants for account 1
SELECT COUNT(*) as assistant_count FROM captain_assistants WHERE account_id = 1;

-- Create default assistant if none exists
INSERT INTO captain_assistants (
  name, 
  description, 
  account_id, 
  config, 
  guardrails, 
  response_guidelines, 
  created_at, 
  updated_at
) 
SELECT 
  'DOTB Assistant',
  'Default AI assistant for DOTB customer support',
  1,
  '{"product_name": "DOTB", "temperature": 0.7}',
  '{}',
  '{}',
  NOW(),
  NOW()
WHERE NOT EXISTS (
  SELECT 1 FROM captain_assistants WHERE account_id = 1
);

-- Verify the assistant was created
SELECT id, name, description, account_id FROM captain_assistants WHERE account_id = 1;
