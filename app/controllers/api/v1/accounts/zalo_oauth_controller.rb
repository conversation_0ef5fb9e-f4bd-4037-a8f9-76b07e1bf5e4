# frozen_string_literal: true

class Api::V1::Accounts::ZaloOauthController < Api::V1::Accounts::BaseController
  skip_before_action :current_account, only: [:callback]
  # GET /api/v1/accounts/:account_id/zalo_oauth/authorize
  def authorize
    oauth_service = Zalo::OauthService.new
    pkce_codes = oauth_service.generate_pkce_codes

    # Store PKCE codes and account info in session
    session[:zalo_code_verifier] = pkce_codes[:code_verifier]
    session[:zalo_state] = generate_state_token
    session[:zalo_account_id] = Current.account.id

    redirect_uri = "#{request.base_url}/zalo/callback"

    # Generate authorization URL
    auth_url = oauth_service.authorization_url(
      redirect_uri,
      session[:zalo_state],
      pkce_codes[:code_challenge]
    )

    render json: {
      authorization_url: auth_url,
      state: session[:zalo_state]
    }
  rescue StandardError => e
    ChatwootExceptionTracker.new(e).capture_exception
    render json: { error: 'Failed to initiate OAuth flow' }, status: :internal_server_error
  end

  # GET /api/v1/accounts/:account_id/zalo_oauth/callback
  def callback
    validate_state_param

    @current_account = Account.find(session[:zalo_account_id])
    Current.account = @current_account

    # The code is the authorization code that we can use to get the access token
    code = params[:code]

    # Exchange the authorization code for an access token
    oauth_service = Zalo::OauthService.new
    token_info = oauth_service.get_access_token(code, session[:zalo_code_verifier])

    # Get Zalo OA info
    zalo_oas = oauth_service.get_oa_info(token_info['access_token'])

    # Create Zalo Channel and Inbox
    zalo_oas.each do |oa|
      create_zalo_channel(token_info, oa)
    end

    # Clear session data
    clear_oauth_session

    # Redirect to inbox setup
    redirect_to "#{frontend_url}/app/accounts/#{Current.account.id}/settings/inboxes/#{@zalo_inbox.id}/settings"

  rescue StandardError => e
    ChatwootExceptionTracker.new(e).capture_exception
    clear_oauth_session
    redirect_to "#{frontend_url}/app/accounts/#{Current.account.id}/settings/inboxes/new?error=oauth_failed"
  end

  private

  def validate_state_param
    return if params[:state] == session[:zalo_state]

    clear_oauth_session
    redirect_to "#{frontend_url}/app/accounts/#{session[:zalo_account_id]}/settings/inboxes/new?error=invalid_state"
  end

  def create_zalo_channel(token_data, oa_info)
    ActiveRecord::Base.transaction do
      # Create Zalo channel with OAuth tokens
      zalo_channel = Current.account.channels.create!(
        type: 'Channel::Zalo',
        oa_access_token: token_data[:access_token],
        refresh_token: token_data[:refresh_token],
        expires_at: Time.current + token_data[:expires_in].seconds,
        oa_id: oa_info.dig('data', 'oa_id'),
        provider_config: {
          oa_name: oa_info.dig('data', 'name'),
          oa_alias: oa_info.dig('data', 'oa_alias'),
          avatar: oa_info.dig('data', 'avatar'),
          oauth_enabled: true
        }
      )

      # Create inbox
      @zalo_inbox = Current.account.inboxes.create!(
        name: oa_info.dig('data', 'name') || 'Zalo OA',
        channel: zalo_channel
      )

      # Set avatar if available
      set_zalo_avatar(@zalo_inbox, oa_info.dig('data', 'avatar'))
    end
  end

  def set_zalo_avatar(inbox, avatar_url)
    return unless avatar_url.present?

    Avatar::AvatarFromUrlJob.perform_later(inbox, avatar_url)
  end

  def generate_state_token
    SecureRandom.hex(16)
  end

  def clear_oauth_session
    session.delete(:zalo_code_verifier)
    session.delete(:zalo_state)
    session.delete(:zalo_account_id)
  end

  def frontend_url
    ENV.fetch('FRONTEND_URL', 'http://localhost:3000')
  end
end
