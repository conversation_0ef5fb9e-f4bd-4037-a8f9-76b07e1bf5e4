<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useStore } from 'dashboard/composables/store';
import Copilot from 'dashboard/components-next/copilot/Copilot.vue';
import { useMapGetter } from 'dashboard/composables/store';
import { useUISettings } from 'dashboard/composables/useUISettings';
import { FEATURE_FLAGS } from 'dashboard/featureFlags';
import CopilotMessagesAPI from 'dashboard/api/captain/copilotMessages';
defineProps({
  conversationInboxType: {
    type: String,
    default: '',
  },
});

const store = useStore();
const currentUser = useMapGetter('getCurrentUser');
// assistants references removed - now using LangGraph agents
const inboxAssistant = useMapGetter('getCopilotAssistant');
const currentChat = useMapGetter('getSelectedChat');

const selectedCopilotThreadId = ref(null);
const messages = computed(() =>
  store.getters['copilotMessages/getMessagesByThreadId'](
    selectedCopilotThreadId.value
  )
);

const currentAccountId = useMapGetter('getCurrentAccountId');
const isFeatureEnabledonAccount = useMapGetter(
  'accounts/isFeatureEnabledonAccount'
);

const selectedAssistantId = ref(null);
const { uiSettings, updateUISettings } = useUISettings();

// Add missing variables
const assistants = useMapGetter('assistants/getAssistants');
const uiFlags = computed(() => ({ fetchingList: false }));

const activeAssistant = computed(() => {
  const preferredId = uiSettings.value.preferred_captain_assistant_id;
  const assistantsList = assistants.value || [];

  // If the user has selected a specific assistant, it takes first preference for Copilot.
  if (preferredId) {
    const preferredAssistant = assistantsList.find(a => a.id === preferredId);
    // Return the preferred assistant if found, otherwise continue to next cases
    if (preferredAssistant) return preferredAssistant;
  }

  // If the above is not available, the assistant connected to the inbox takes preference.
  if (inboxAssistant.value) {
    const inboxMatchedAssistant = assistantsList.find(
      a => a.id === inboxAssistant.value.id
    );
    if (inboxMatchedAssistant) return inboxMatchedAssistant;
  }

  // If neither of the above is available, the first assistant in the account takes preference.
  const firstAssistant = assistantsList[0];
  if (firstAssistant) return firstAssistant;

  // TEMPORARY FIX: Since assistant routes were removed for LangGraph system,
  // but CopilotThread still requires assistant_id, use the known assistant ID
  // TODO: Remove this when backend is fully migrated to LangGraph
  return {
    id: 1, // Known assistant ID from database
    name: 'DOTB Assistant',
    description: 'Default DOTB AI assistant'
  };
});

const setAssistant = async assistant => {
  selectedAssistantId.value = assistant.id;
  await updateUISettings({
    preferred_captain_assistant_id: assistant.id,
  });
};

const shouldShowCopilotPanel = computed(() => {
  const isCaptainEnabled = isFeatureEnabledonAccount.value(
    currentAccountId.value,
    FEATURE_FLAGS.CAPTAIN
  );
  const { is_copilot_panel_open: isCopilotPanelOpen } = uiSettings.value;
  return isCaptainEnabled && isCopilotPanelOpen && !uiFlags.value.fetchingList;
});

const handleReset = () => {
  selectedCopilotThreadId.value = null;
};

const sendMessage = async message => {
  // Check if activeAssistant is available
  if (!activeAssistant.value || !activeAssistant.value.id) {
    console.error('No active assistant available. Cannot send message.');
    // Show user-friendly error message
    alert('Không có trợ lý AI nào được cấu hình. Vui lòng liên hệ quản trị viên để thiết lập Captain Assistant.');
    return;
  }

  try {
    if (selectedCopilotThreadId.value) {
      await store.dispatch('copilotMessages/create', {
        assistant_id: activeAssistant.value.id,
        conversation_id: currentChat.value?.id,
        threadId: selectedCopilotThreadId.value,
        message,
      });
    } else {
      const response = await store.dispatch('copilotThreads/create', {
        assistant_id: activeAssistant.value.id,
        conversation_id: currentChat.value?.id,
        message,
      });
      selectedCopilotThreadId.value = response.id;
    }
  } catch (error) {
    console.error('Error sending message:', error);
    alert('Có lỗi xảy ra khi gửi tin nhắn. Vui lòng thử lại.');
  }
};

const fetchMessagesForThread = async (threadId) => {
  if (!threadId) return;

  try {
    const response = await CopilotMessagesAPI.get(threadId);
    // Add messages to store
    if (response.data && response.data.payload) {
      response.data.payload.forEach(message => {
        store.dispatch('copilotMessages/upsert', message);
      });
    }
  } catch (error) {
    console.error('Error fetching copilot messages:', error);
  }
};

// Watch for thread ID changes and fetch messages
watch(
  () => selectedCopilotThreadId.value,
  (newThreadId) => {
    if (newThreadId) {
      fetchMessagesForThread(newThreadId);
    }
  },
  { immediate: true }
);

onMounted(() => {
  // Assistant dispatch removed - now using LangGraph agents
  // Fetch messages for current thread if any
  if (selectedCopilotThreadId.value) {
    fetchMessagesForThread(selectedCopilotThreadId.value);
  }
});
</script>

<template>
  <div
    v-if="shouldShowCopilotPanel"
    class="ltr:border-l rtl:border-r border-n-weak h-full overflow-hidden z-10 w-[320px] min-w-[320px] 2xl:min-w-[360px] 2xl:w-[360px] flex flex-col bg-n-background"
  >
    <Copilot
      :messages="messages"
      :support-agent="currentUser"
      :conversation-inbox-type="conversationInboxType"
      :assistants="assistants"
      :active-assistant="activeAssistant"
      @set-assistant="setAssistant"
      @send-message="sendMessage"
      @reset="handleReset"
    />
  </div>
  <template v-else />
</template>
