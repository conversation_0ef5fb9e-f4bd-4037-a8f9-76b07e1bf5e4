#!/usr/bin/env python3
"""
LangGraph Workflow Service for DOTB Captain
Provides HTTP API for conversational AI workflows with tool calling capabilities
"""

import os
import json
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import FastAP<PERSON>, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import MessagesState
from langchain_anthropic import ChatAnthropic
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.tools import tool
import httpx
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Set debug level for development
if os.getenv('ENV') == 'development':
    logging.getLogger().setLevel(logging.DEBUG)

app = FastAPI(title="DOTB LangGraph Service", version="1.0.0")
security = HTTPBearer()

# Request logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    start_time = datetime.now()
    logger.info(f"Request: {request.method} {request.url}")

    response = await call_next(request)

    process_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"Response: {response.status_code} - {process_time:.3f}s")

    return response

# Configuration
RAILS_CALLBACK_BASE_URL = os.getenv('RAILS_CALLBACK_BASE_URL', 'http://localhost:3000')
ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
LANGGRAPH_API_KEY = os.getenv('LANGGRAPH_API_KEY')

class ChatRequest(BaseModel):
    assistant_id: int
    account_id: int
    user_id: int
    conversation_id: Optional[int] = None
    input: str
    workflow_config: Dict[str, Any]
    tools: List[Dict[str, Any]]
    previous_history: List[Dict[str, Any]] = []

class ChatResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class WorkflowState(MessagesState):
    """Extended state for DOTB workflow"""
    assistant_id: int
    account_id: int
    user_id: int
    workflow_config: Dict[str, Any]
    tools_registry: Dict[str, Any]
    callback_base_url: str
    auth_headers: Dict[str, str]

# Authentication
def verify_token(credentials: HTTPAuthorizationCredentials = Security(security)):
    if credentials.credentials != LANGGRAPH_API_KEY:
        raise HTTPException(status_code=401, detail="Invalid authentication credentials")
    return credentials.credentials

def get_chatwoot_headers(credentials: HTTPAuthorizationCredentials = Security(security)):
    """Convert Bearer token to Chatwoot api_access_token header"""
    if not credentials:
        raise HTTPException(status_code=401, detail="Authentication required")

    return {
        "api_access_token": credentials.credentials,
        "Content-Type": "application/json"
    }

async def fetch_conversation_messages(
    account_id: int,
    conversation_id: int,
    headers: Dict[str, str],
    limit: int = 20
) -> List[Dict[str, Any]]:
    """Fetch conversation messages from Chatwoot"""
    url = f"{RAILS_CALLBACK_BASE_URL}/api/v1/accounts/{account_id}/conversations/{conversation_id}/messages"

    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            logger.info(f"Fetching messages for conversation {conversation_id}")
            response = await client.get(url, headers=headers, params={"limit": limit})
            response.raise_for_status()

            messages = response.json()
            logger.info(f"Fetched {len(messages)} messages")

            # Debug: Log messages structure
            logger.info(f"Messages response type: {type(messages)}")
            if messages:
                logger.info(f"First message sample: {messages[0] if len(messages) > 0 else 'No messages'}")
            else:
                logger.info("Messages is empty or None")

            return messages
        except Exception as e:
            logger.error(f"Error fetching conversation messages: {e}")
            return []

async def fetch_conversation_details(
    account_id: int,
    conversation_id: int,
    headers: Dict[str, str]
) -> Dict[str, Any]:
    """Fetch conversation details from Chatwoot"""
    url = f"{RAILS_CALLBACK_BASE_URL}/api/v1/accounts/{account_id}/conversations/{conversation_id}"

    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            logger.info(f"Fetching details for conversation {conversation_id}")
            response = await client.get(url, headers=headers)
            response.raise_for_status()

            conversation = response.json()
            logger.info(f"Fetched conversation details: {conversation.get('id')}")
            return conversation
        except Exception as e:
            logger.error(f"Error fetching conversation details: {e}")
            return {}

def build_conversation_context(
    messages: List[Dict[str, Any]],
    conversation: Dict[str, Any]
) -> str:
    """Build conversation context string from messages and conversation details"""
    context_parts = []

    # Add conversation metadata
    if conversation:
        context_parts.append(f"Conversation ID: {conversation.get('id')}")
        context_parts.append(f"Status: {conversation.get('status')}")

        # Add contact info
        if 'meta' in conversation and 'sender' in conversation['meta']:
            sender = conversation['meta']['sender']
            context_parts.append(f"Contact: {sender.get('name', 'Unknown')} ({sender.get('email', 'No email')})")

        # Add labels
        if conversation.get('labels'):
            context_parts.append(f"Labels: {', '.join(conversation['labels'])}")

    # Add recent messages
    if messages and isinstance(messages, list):
        context_parts.append("\nRecent conversation:")
        recent_messages = messages[-10:] if len(messages) > 10 else messages
        for msg in recent_messages:
            sender_type = msg.get('sender_type', 'unknown')
            content = msg.get('content', '')

            if sender_type == 'User':
                context_parts.append(f"Agent: {content}")
            elif sender_type == 'Contact':
                context_parts.append(f"Customer: {content}")
            else:
                context_parts.append(f"System: {content}")

    return "\n".join(context_parts)

def detect_special_actions(input_text: str) -> str:
    """Detect special actions from user input"""
    input_lower = input_text.lower().strip()

    # Summarization patterns
    summarize_patterns = [
        'tóm tắt cuộc hội thoại',
        'tóm tắt cuộc trò chuyện',
        'tóm tắt conversation',
        'summarize conversation',
        'summarize this conversation',
        'tóm tắt',
        'summary'
    ]

    # Reply suggestion patterns
    reply_patterns = [
        'gợi ý câu trả lời',
        'gợi ý phản hồi',
        'suggest reply',
        'draft response',
        'draft a response',
        'viết câu trả lời',
        'soạn câu trả lời'
    ]

    # Rating patterns
    rating_patterns = [
        'đánh giá cuộc hội thoại',
        'đánh giá conversation',
        'rate conversation',
        'rate this conversation',
        'đánh giá',
        'rating'
    ]

    for pattern in summarize_patterns:
        if pattern in input_lower:
            return 'summarize_conversation'

    for pattern in reply_patterns:
        if pattern in input_lower:
            return 'draft_response'

    for pattern in rating_patterns:
        if pattern in input_lower:
            return 'rate_conversation'

    return 'normal'

async def handle_special_action(
    action_type: str,
    request: ChatRequest,
    messages: List[Dict[str, Any]],
    conversation: Dict[str, Any],
    conversation_context: str
) -> ChatResponse:
    """Handle special actions like summarization, reply suggestions, and rating"""

    if action_type == 'summarize_conversation':
        return await handle_summarize_conversation(messages, conversation, conversation_context)
    elif action_type == 'draft_response':
        return await handle_draft_response(messages, conversation, conversation_context)
    elif action_type == 'rate_conversation':
        return await handle_rate_conversation(messages, conversation, conversation_context)
    else:
        return ChatResponse(
            success=False,
            error=f"Unknown action type: {action_type}"
        )

async def handle_summarize_conversation(
    messages: List[Dict[str, Any]],
    conversation: Dict[str, Any],
    conversation_context: str
) -> ChatResponse:
    """Handle conversation summarization"""

    if not messages:
        return ChatResponse(
            success=True,
            data={
                'content': 'Không có tin nhắn nào để tóm tắt trong cuộc hội thoại này.',
                'type': 'text',
                'timestamp': datetime.now().isoformat(),
                'action_type': 'summarize_conversation'
            }
        )

    # Build summary from messages
    summary_parts = []

    # Add conversation metadata
    if conversation:
        summary_parts.append(f"**Thông tin cuộc hội thoại:**")
        summary_parts.append(f"- ID: {conversation.get('id', 'N/A')}")
        summary_parts.append(f"- Trạng thái: {conversation.get('status', 'N/A')}")

        if 'meta' in conversation and 'sender' in conversation['meta']:
            sender = conversation['meta']['sender']
            summary_parts.append(f"- Khách hàng: {sender.get('name', 'Unknown')} ({sender.get('email', 'No email')})")

    # Analyze messages
    customer_messages = []
    agent_messages = []

    for msg in messages:
        # Handle both dict and string messages
        if isinstance(msg, str):
            content = msg.strip()
            sender_type = 'unknown'
        else:
            content = msg.get('content', '').strip()
            sender_type = msg.get('sender_type', 'unknown')

        if not content:
            continue

        if sender_type == 'Contact':
            customer_messages.append(content)
        elif sender_type == 'User':
            agent_messages.append(content)
        else:
            # For unknown sender types, try to categorize by content
            agent_messages.append(content)

    summary_parts.append(f"\n**Tóm tắt cuộc hội thoại:**")
    summary_parts.append(f"- Tổng số tin nhắn: {len(messages)}")
    summary_parts.append(f"- Tin nhắn từ khách hàng: {len(customer_messages)}")
    summary_parts.append(f"- Tin nhắn từ đại lý: {len(agent_messages)}")

    if customer_messages:
        summary_parts.append(f"\n**Các vấn đề chính của khách hàng:**")
        for i, msg in enumerate(customer_messages[:3], 1):  # Show first 3 customer messages
            summary_parts.append(f"{i}. {msg[:100]}{'...' if len(msg) > 100 else ''}")

    if agent_messages:
        summary_parts.append(f"\n**Phản hồi của đại lý:**")
        for i, msg in enumerate(agent_messages[:3], 1):  # Show first 3 agent messages
            summary_parts.append(f"{i}. {msg[:100]}{'...' if len(msg) > 100 else ''}")

    summary_content = "\n".join(summary_parts)

    return ChatResponse(
        success=True,
        data={
            'content': summary_content,
            'type': 'text',
            'timestamp': datetime.now().isoformat(),
            'action_type': 'summarize_conversation',
            'usage_info': {
                'total_messages': len(messages),
                'customer_messages': len(customer_messages),
                'agent_messages': len(agent_messages)
            }
        }
    )

async def handle_draft_response(
    messages: List[Dict[str, Any]],
    conversation: Dict[str, Any],
    conversation_context: str
) -> ChatResponse:
    """Handle reply suggestion"""

    if not messages:
        return ChatResponse(
            success=True,
            data={
                'content': 'Không có tin nhắn nào để gợi ý phản hồi.',
                'type': 'text',
                'timestamp': datetime.now().isoformat(),
                'action_type': 'draft_response'
            }
        )

    # Find the last customer message
    last_customer_message = None
    for msg in reversed(messages):
        if isinstance(msg, str):
            last_customer_message = msg.strip()
            break
        elif msg.get('sender_type') == 'Contact':
            last_customer_message = msg.get('content', '').strip()
            break

    if not last_customer_message:
        return ChatResponse(
            success=True,
            data={
                'content': 'Không tìm thấy tin nhắn nào từ khách hàng để gợi ý phản hồi.',
                'type': 'text',
                'timestamp': datetime.now().isoformat(),
                'action_type': 'draft_response'
            }
        )

    # Generate response suggestions based on common patterns
    suggestions = []

    # Analyze customer message for common patterns
    message_lower = last_customer_message.lower()

    if any(word in message_lower for word in ['xin chào', 'hello', 'hi', 'chào']):
        suggestions.append("Xin chào! Cảm ơn bạn đã liên hệ với chúng tôi. Tôi có thể giúp gì cho bạn hôm nay?")

    if any(word in message_lower for word in ['cảm ơn', 'thank', 'thanks']):
        suggestions.append("Rất vui được hỗ trợ bạn! Nếu bạn có thêm câu hỏi nào khác, đừng ngần ngại liên hệ với chúng tôi.")

    if any(word in message_lower for word in ['vấn đề', 'problem', 'issue', 'lỗi', 'error']):
        suggestions.append("Tôi hiểu vấn đề bạn đang gặp phải. Để hỗ trợ bạn tốt nhất, bạn có thể cung cấp thêm chi tiết về vấn đề này không?")

    if any(word in message_lower for word in ['giá', 'price', 'cost', 'phí']):
        suggestions.append("Cảm ơn bạn quan tâm đến sản phẩm/dịch vụ của chúng tôi. Tôi sẽ gửi bảng giá chi tiết cho bạn ngay.")

    # Default suggestions if no pattern matches
    if not suggestions:
        suggestions = [
            "Cảm ơn bạn đã liên hệ. Tôi đã ghi nhận yêu cầu của bạn và sẽ hỗ trợ bạn ngay.",
            "Để tôi có thể hỗ trợ bạn tốt nhất, bạn có thể cung cấp thêm thông tin chi tiết không?",
            "Tôi sẽ kiểm tra và phản hồi lại bạn trong thời gian sớm nhất."
        ]

    response_content = f"**Tin nhắn cuối từ khách hàng:**\n{last_customer_message}\n\n**Gợi ý câu trả lời:**\n"
    for i, suggestion in enumerate(suggestions[:3], 1):
        response_content += f"{i}. {suggestion}\n"

    return ChatResponse(
        success=True,
        data={
            'content': response_content,
            'type': 'text',
            'timestamp': datetime.now().isoformat(),
            'action_type': 'draft_response',
            'reply_suggestion': True,
            'suggestions': suggestions[:3]
        }
    )

async def handle_rate_conversation(
    messages: List[Dict[str, Any]],
    conversation: Dict[str, Any],
    conversation_context: str
) -> ChatResponse:
    """Handle conversation rating"""

    if not messages:
        return ChatResponse(
            success=True,
            data={
                'content': 'Không có tin nhắn nào để đánh giá trong cuộc hội thoại này.',
                'type': 'text',
                'timestamp': datetime.now().isoformat(),
                'action_type': 'rate_conversation'
            }
        )

    # Analyze conversation quality
    total_messages = len(messages)
    customer_messages = []
    agent_messages = []

    for msg in messages:
        if isinstance(msg, str):
            # For string messages, assume they are customer messages
            customer_messages.append(msg)
        elif msg.get('sender_type') == 'Contact':
            customer_messages.append(msg)
        elif msg.get('sender_type') == 'User':
            agent_messages.append(msg)

    # Calculate metrics
    response_ratio = len(agent_messages) / len(customer_messages) if customer_messages else 0

    # Calculate average response length
    total_length = 0
    for msg in agent_messages:
        if isinstance(msg, str):
            total_length += len(msg)
        else:
            total_length += len(msg.get('content', ''))
    avg_response_length = total_length / len(agent_messages) if agent_messages else 0

    # Determine rating based on metrics
    rating_score = 3  # Default neutral
    rating_reasons = []

    if response_ratio >= 1.0:
        rating_score += 1
        rating_reasons.append("Đại lý phản hồi đầy đủ cho tất cả tin nhắn của khách hàng")
    elif response_ratio < 0.5:
        rating_score -= 1
        rating_reasons.append("Đại lý chưa phản hồi đầy đủ cho khách hàng")

    if avg_response_length > 50:
        rating_score += 0.5
        rating_reasons.append("Phản hồi của đại lý có nội dung chi tiết")
    elif avg_response_length < 20:
        rating_score -= 0.5
        rating_reasons.append("Phản hồi của đại lý khá ngắn gọn")

    if total_messages > 10:
        rating_score -= 0.5
        rating_reasons.append("Cuộc hội thoại kéo dài, có thể cần cải thiện hiệu quả")

    # Cap rating between 1 and 5
    rating_score = max(1, min(5, rating_score))

    # Convert to star rating
    stars = "⭐" * int(rating_score)
    if rating_score % 1 >= 0.5:
        stars += "⭐"

    rating_text = {
        1: "Cần cải thiện nhiều",
        2: "Cần cải thiện",
        3: "Trung bình",
        4: "Tốt",
        5: "Xuất sắc"
    }.get(int(rating_score), "Trung bình")

    response_content = f"**Đánh giá cuộc hội thoại:**\n\n"
    response_content += f"**Điểm số:** {rating_score:.1f}/5.0 {stars}\n"
    response_content += f"**Mức độ:** {rating_text}\n\n"
    response_content += f"**Thống kê:**\n"
    response_content += f"- Tổng tin nhắn: {total_messages}\n"
    response_content += f"- Tin nhắn khách hàng: {len(customer_messages)}\n"
    response_content += f"- Tin nhắn đại lý: {len(agent_messages)}\n"
    response_content += f"- Tỷ lệ phản hồi: {response_ratio:.1f}\n\n"

    if rating_reasons:
        response_content += f"**Lý do đánh giá:**\n"
        for reason in rating_reasons:
            response_content += f"- {reason}\n"

    return ChatResponse(
        success=True,
        data={
            'content': response_content,
            'type': 'text',
            'timestamp': datetime.now().isoformat(),
            'action_type': 'rate_conversation',
            'rating': {
                'score': rating_score,
                'stars': int(rating_score),
                'text': rating_text,
                'reasons': rating_reasons
            }
        }
    )

# Tool execution via Rails callback
async def execute_rails_tool(
    tool_name: str,
    arguments: Dict[str, Any],
    assistant_id: int,
    account_id: int,
    user_id: int,
    headers: Dict[str, str]
) -> str:
    """Execute tool by calling back to Rails service"""
    callback_url = f"{RAILS_CALLBACK_BASE_URL}/api/v1/accounts/{account_id}/captain/langgraph/execute_tool"

    payload = {
        'assistant_id': assistant_id,
        'user_id': user_id,
        'tool_name': tool_name,
        'arguments': arguments
    }

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            logger.info(f"Executing tool {tool_name} for account {account_id}, assistant {assistant_id}")
            logger.debug(f"Tool payload: {payload}")

            response = await client.post(callback_url, json=payload, headers=headers)
            response.raise_for_status()

            result = response.json()
            logger.debug(f"Tool response: {result}")

            if result.get('success'):
                logger.info(f"Tool {tool_name} executed successfully")
                return str(result.get('result', ''))
            else:
                error_msg = result.get('error', 'Unknown error')
                logger.error(f"Tool execution failed: {error_msg}")
                return f"Tool execution failed: {error_msg}"
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error calling Rails tool {tool_name}: {e.response.status_code} - {e.response.text}")
            return f"HTTP error executing tool {tool_name}: {e.response.status_code}"
        except httpx.TimeoutException:
            logger.error(f"Timeout calling Rails tool {tool_name}")
            return f"Timeout executing tool {tool_name}"
        except Exception as e:
            logger.error(f"Error calling Rails tool {tool_name}: {e}")
            return f"Error executing tool {tool_name}: {str(e)}"

# LangGraph workflow nodes
async def assistant_node(state: WorkflowState) -> WorkflowState:
    """Main assistant reasoning node"""
    config = state['workflow_config']
    tools_registry = state['tools_registry']
    
    # Convert tools to LangChain format
    tools = []
    # tools_registry is now a dict
    for tool_name, tool_info in tools_registry.items():
        from langchain_core.tools import StructuredTool
        
        # Extract tool info from Rails format
        function_info = tool_info.get('function', {})
        tool_description = function_info.get('description', f'Execute {tool_name}')
        
        # Create a dummy function that returns the tool name for binding
        def make_tool_func(name):
            def tool_func(**kwargs):
                return f"Calling {name} with {kwargs}"
            return tool_func
        
        tool = StructuredTool.from_function(
            func=make_tool_func(tool_name),
            name=tool_name,
            description=tool_description,
            args_schema=None  # We'll handle schema later if needed
        )
        tools.append(tool)
    
    # Initialize LLM with tools
    llm = ChatAnthropic(
        model=config.get('model', 'claude-3-5-sonnet-********'),
        temperature=config.get('temperature', 1.0),
        api_key=ANTHROPIC_API_KEY
    )
    
    # Bind tools to LLM
    if tools:
        llm = llm.bind_tools(tools)
    
    # Build messages
    messages = []
    
    # System prompt
    if config.get('system_prompt'):
        messages.append(SystemMessage(content=config['system_prompt']))
    
    # Account context
    if config.get('account_context'):
        messages.append(SystemMessage(content=config['account_context']))
    
    # Conversation context
    if config.get('conversation_context'):
        messages.append(SystemMessage(content=config['conversation_context']['context']))
    
    # Previous history
    for msg in state.get('previous_history', []):
        if msg.get('role') == 'user':
            messages.append(HumanMessage(content=msg['content']))
        elif msg.get('role') == 'assistant':
            # Handle array content from LangGraph responses
            content = msg['content']
            tool_calls = []

            if isinstance(content, list):
                # Extract text parts and tool_use parts
                text_parts = []
                for part in content:
                    if part.get('type') == 'text':
                        text_parts.append(part.get('text', ''))
                    elif part.get('type') == 'tool_use':
                        # Convert to LangGraph tool_call format
                        tool_calls.append({
                            'name': part.get('name'),
                            'args': part.get('input', {}),
                            'id': part.get('id')
                        })

                # Join text parts
                content = '\n\n'.join(text_parts)

            # Create AIMessage with tool_calls if any
            if tool_calls:
                messages.append(AIMessage(content=content, tool_calls=tool_calls))
            else:
                messages.append(AIMessage(content=content))
    
    # Current messages from state
    messages.extend(state['messages'])
    
    # Get LLM response
    response = await llm.ainvoke(messages)
    
    # Update state with new message
    return {"messages": [response]}

async def should_continue(state: WorkflowState):
    """Determine if we should continue to tools or end"""
    messages = state["messages"]
    last_message = messages[-1]
    
    # If the LLM makes a tool call, then we route to the "tools" node
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "tools"
    # Otherwise, we stop (reply to the user)
    return END

async def call_tools(state: WorkflowState):
    """Execute the tools requested by the LLM"""
    messages = state["messages"]
    last_message = messages[-1]
    
    # No tool calls to execute
    if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
        return {"messages": []}
    
    tool_messages = []
    
    for tool_call in last_message.tool_calls:
        tool_name = tool_call["name"]
        arguments = tool_call["args"]
        tool_call_id = tool_call["id"]
        
        logger.info(f"Executing tool: {tool_name} with args: {arguments}")
        
        # Execute tool via Rails callback
        result = await execute_rails_tool(
            tool_name,
            arguments,
            state['assistant_id'],
            state['account_id'],
            state['user_id'],
            state['auth_headers']
        )
        
        # Create tool message with result
        from langchain_core.messages import ToolMessage
        tool_message = ToolMessage(
            content=str(result),
            tool_call_id=tool_call_id
        )
        tool_messages.append(tool_message)
    
    return {"messages": tool_messages}

# Build workflow graph
def create_workflow():
    """Create the LangGraph workflow"""
    workflow = StateGraph(WorkflowState)
    
    # Add nodes
    workflow.add_node("assistant", assistant_node)
    workflow.add_node("tools", call_tools)
    
    # Set entry point
    workflow.add_edge(START, "assistant")
    
    # Add conditional edge
    workflow.add_conditional_edges(
        "assistant",
        should_continue,
        {
            "tools": "tools",
            END: END,
        }
    )
    
    # Add edge from tools back to assistant
    workflow.add_edge("tools", "assistant")
    
    return workflow.compile()

# Global workflow instance
workflow_app = create_workflow()

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(
    request: ChatRequest,
    credentials: HTTPAuthorizationCredentials = Security(security)
):
    """Main chat endpoint using Multi-Agent System"""
    try:
        logger.info(f"Multi-Agent chat request for assistant {request.assistant_id}: {request.input}")

        # Get Chatwoot headers for API calls
        auth_headers = get_chatwoot_headers(credentials)

        # Detect special actions (keep existing logic for backward compatibility)
        action_type = detect_special_actions(request.input)
        logger.info(f"Detected action type: {action_type}")

        # Fetch conversation context if conversation_id is provided
        conversation_context = ""
        messages = []
        conversation = {}

        if request.conversation_id:
            messages = await fetch_conversation_messages(
                request.account_id,
                request.conversation_id,
                auth_headers
            )
            conversation = await fetch_conversation_details(
                request.account_id,
                request.conversation_id,
                auth_headers
            )
            conversation_context = build_conversation_context(messages, conversation)

            # Add conversation context to workflow config
            if 'conversation_context' not in request.workflow_config:
                request.workflow_config['conversation_context'] = {}
            request.workflow_config['conversation_context']['context'] = conversation_context

        # Handle special actions with legacy system for now
        if action_type != 'normal':
            return await handle_special_action(
                action_type,
                request,
                messages,
                conversation,
                conversation_context
            )

        # Use Multi-Agent System for normal requests
        from coordination.orchestrator import orchestrator
        
        # Prepare request data for orchestrator
        orchestrator_request = {
            'input': request.input,
            'assistant_id': request.assistant_id,
            'account_id': request.account_id,
            'user_id': request.user_id,
            'conversation_id': request.conversation_id,
            'workflow_config': request.workflow_config,
            'tools': request.tools,
            'previous_history': request.previous_history,
            'auth_headers': auth_headers
        }
        
        # Process with orchestrator
        orchestrator_result = await orchestrator.process_request(orchestrator_request)
        
        if not orchestrator_result.get('success', False):
            logger.error(f"Orchestrator error: {orchestrator_result.get('error')}")
            return ChatResponse(
                success=False,
                error=orchestrator_result.get('error', 'Unknown orchestration error')
            )
        
        # Extract response data from orchestrator result
        agent_data = orchestrator_result.get('data', {})
        orchestration_info = orchestrator_result.get('orchestration', {})
        
        # Extract content from agent response
        response_content = ""
        if 'messages' in agent_data and agent_data['messages']:
            final_message = agent_data['messages'][-1]
            response_content = final_message.content if hasattr(final_message, 'content') else str(final_message)
        elif 'content' in agent_data:
            response_content = agent_data['content']
        else:
            response_content = str(agent_data)

        # Count tool usage from agent responses
        tool_calls_count = 0
        tool_results = []
        
        if 'messages' in agent_data:
            for msg in agent_data['messages']:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    tool_calls_count += len(msg.tool_calls)
                    for tool_call in msg.tool_calls:
                        tool_results.append({
                            'tool_name': tool_call.get('name'),
                            'arguments': tool_call.get('args'),
                            'id': tool_call.get('id')
                        })

        # Prepare enhanced response data
        response_data = {
            'content': response_content,
            'type': 'text',
            'timestamp': datetime.now().isoformat(),
            'usage_info': {
                'tools_used': tool_calls_count,
                'tool_results': tool_results,
                'agent_used': orchestrator_result.get('agent_used') or orchestrator_result.get('agents_used'),
                'routing_type': orchestrator_result.get('routing_type'),
                'processing_time': orchestration_info.get('processing_time_seconds')
            },
            'conversation_id': request.conversation_id,
            'assistant_id': request.assistant_id,
            'account_id': request.account_id,
            'multi_agent_info': {
                'orchestration_strategy': orchestration_info.get('strategy_used'),
                'request_id': orchestration_info.get('request_id'),
                'agents_involved': orchestrator_result.get('agents_used') or [orchestrator_result.get('agent_used')]
            }
        }

        logger.info(f"Multi-Agent chat completed for assistant {request.assistant_id}, "
                   f"agent(s): {orchestrator_result.get('agent_used') or orchestrator_result.get('agents_used')}, "
                   f"tools used: {tool_calls_count}")
        
        return ChatResponse(success=True, data=response_data)

    except HTTPException:
        # Re-raise HTTP exceptions (like auth errors)
        raise
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during multi-agent chat processing: {e.response.status_code} - {e.response.text}")
        return ChatResponse(
            success=False,
            error=f"External service error: {e.response.status_code}"
        )
    except httpx.TimeoutException:
        logger.error("Timeout during multi-agent chat processing")
        return ChatResponse(
            success=False,
            error="Request timeout - please try again"
        )
    except Exception as e:
        logger.error(f"Unexpected error during multi-agent chat processing: {e}", exc_info=True)
        return ChatResponse(
            success=False,
            error="Internal server error - please try again"
        )

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "langgraph-workflow",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/workflow/info")
async def workflow_info(token: str = Depends(verify_token)):
    """Get workflow information"""
    return {
        "nodes": ["assistant", "tools"],
        "capabilities": [
            "multi-turn conversations",
            "tool calling",
            "RAG integration",
            "stateful workflows",
            "multi-agent coordination",
            "intelligent routing"
        ],
        "supported_tools": [
            "search_documentation",
            "search_articles", 
            "search_contacts",
            "search_conversations",
            "get_article",
            "get_contact",
            "get_conversation"
        ]
    }

@app.get("/agents")
async def list_agents(token: str = Depends(verify_token)):
    """List all available agents"""
    from agents.agent_registry import agent_registry
    return {
        "available_agents": agent_registry.get_available_agents(),
        "agents_info": agent_registry.get_all_agents_info(),
        "registry_stats": agent_registry.get_registry_stats()
    }

@app.get("/agents/{agent_type}")
async def get_agent_info(agent_type: str, token: str = Depends(verify_token)):
    """Get detailed information about a specific agent"""
    from agents.agent_registry import agent_registry
    
    agent_info = agent_registry.get_agent_info(agent_type)
    if not agent_info:
        raise HTTPException(status_code=404, detail=f"Agent {agent_type} not found")
    
    # Get health status
    health_status = agent_registry.validate_agent_health(agent_type)
    
    return {
        "agent_info": agent_info,
        "health_status": health_status
    }

@app.get("/agents/{agent_type}/health")
async def check_agent_health(agent_type: str, token: str = Depends(verify_token)):
    """Check health status of a specific agent"""
    from agents.agent_registry import agent_registry
    return agent_registry.validate_agent_health(agent_type)

@app.get("/orchestrator/stats")
async def get_orchestrator_stats(token: str = Depends(verify_token)):
    """Get orchestrator statistics"""
    from coordination.orchestrator import orchestrator
    return orchestrator.get_orchestrator_stats()

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv('PORT', 8000))
    host = os.getenv('HOST', '0.0.0.0')
    
    logger.info(f"Starting LangGraph service on {host}:{port}")
    logger.info(f"Rails callback URL: {RAILS_CALLBACK_BASE_URL}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=os.getenv('ENV') == 'development',
        log_level="info"
    )