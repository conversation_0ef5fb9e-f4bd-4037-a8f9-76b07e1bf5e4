#!/usr/bin/env ruby

# Script to create a default Captain assistant for the account
# This is needed because the copilot functionality requires an assistant to work

# Load Rails environment
require_relative 'config/environment'

# Find the account (assuming account ID 1 based on the URL we saw)
account = Account.find(1)

# Check if there are any assistants for this account
existing_assistants = account.captain_assistants

puts "Account: #{account.name} (ID: #{account.id})"
puts "Existing assistants: #{existing_assistants.count}"

if existing_assistants.empty?
  puts "Creating default assistant..."
  
  assistant = account.captain_assistants.create!(
    name: 'DOTB Assistant',
    description: 'Default AI assistant for DOTB customer support',
    config: {
      product_name: 'DOTB',
      temperature: 0.7
    },
    guardrails: {},
    response_guidelines: {}
  )
  
  puts "Created assistant: #{assistant.name} (ID: #{assistant.id})"
else
  puts "Assistants already exist:"
  existing_assistants.each do |assistant|
    puts "- #{assistant.name} (ID: #{assistant.id})"
  end
end
